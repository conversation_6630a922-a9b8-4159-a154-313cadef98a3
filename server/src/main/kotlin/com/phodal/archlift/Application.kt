package com.phodal.archlift

import com.phodal.archlift.mcp.client.McpClientManager
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.engine.*
import io.ktor.server.netty.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json

fun main() {
    embeddedServer(Netty, port = SERVER_PORT, host = "0.0.0.0", module = Application::module)
        .start(wait = true)
}

fun Application.module() {
    // 配置 JSON 序列化
    install(ContentNegotiation) {
        json(Json {
            prettyPrint = true
            isLenient = true
            ignoreUnknownKeys = true
        })
    }

    // 初始化 MCP 组件
    val clientManager = McpClientManager()

    // 加载配置
    launch {
        clientManager.loadConfig()
    }

    routing {
        get("/") {
            call.respondText("ArchLift MCP Client: ${Greeting().greet()}")
        }

        get("/api/mcp/health") {
            call.respond(HttpStatusCode.OK, mapOf("status" to "ok", "message" to "MCP Client is running"))
        }

        get("/api/mcp/servers") {
            val statuses = clientManager.servers.values.map { instance ->
                mapOf(
                    "name" to instance.name,
                    "status" to instance.status.name,
                    "transport" to instance.config.transport.name,
                    "command" to instance.config.command,
                    "url" to instance.config.url,
                    "disabled" to instance.config.disabled.toString()
                )
            }
            call.respond(HttpStatusCode.OK, statuses)
        }

        // 启动服务器
        post("/api/mcp/servers/{name}/start") {
            val serverName = call.parameters["name"] ?: return@post call.respond(
                HttpStatusCode.BadRequest,
                mapOf("error" to "Server name is required")
            )

            val result = clientManager.startServer(serverName)
            if (result.isSuccess) {
                call.respond(HttpStatusCode.OK, mapOf("message" to "Server started successfully"))
            } else {
                call.respond(
                    HttpStatusCode.InternalServerError,
                    mapOf("error" to result.exceptionOrNull()?.message)
                )
            }
        }

        // 停止服务器
        post("/api/mcp/servers/{name}/stop") {
            val serverName = call.parameters["name"] ?: return@post call.respond(
                HttpStatusCode.BadRequest,
                mapOf("error" to "Server name is required")
            )

            val result = clientManager.stopServer(serverName)
            if (result.isSuccess) {
                call.respond(HttpStatusCode.OK, mapOf("message" to "Server stopped successfully"))
            } else {
                call.respond(
                    HttpStatusCode.InternalServerError,
                    mapOf("error" to result.exceptionOrNull()?.message)
                )
            }
        }

        // 重启服务器
        post("/api/mcp/servers/{name}/restart") {
            val serverName = call.parameters["name"] ?: return@post call.respond(
                HttpStatusCode.BadRequest,
                mapOf("error" to "Server name is required")
            )

            val result = clientManager.restartServer(serverName)
            if (result.isSuccess) {
                call.respond(HttpStatusCode.OK, mapOf("message" to "Server restarted successfully"))
            } else {
                call.respond(
                    HttpStatusCode.InternalServerError,
                    mapOf("error" to result.exceptionOrNull()?.message)
                )
            }
        }

        // 列出服务器工具
        get("/api/mcp/servers/{name}/tools") {
            val serverName = call.parameters["name"] ?: return@get call.respond(
                HttpStatusCode.BadRequest,
                mapOf("error" to "Server name is required")
            )

            val result = clientManager.listTools(serverName)
            if (result.isSuccess) {
                call.respond(HttpStatusCode.OK, result.getOrNull() ?: emptyList<Map<String, Any>>())
            } else {
                call.respond(
                    HttpStatusCode.InternalServerError,
                    mapOf("error" to result.exceptionOrNull()?.message)
                )
            }
        }

        // 调用工具
        post("/api/mcp/servers/{name}/tools/{toolName}") {
            val serverName = call.parameters["name"] ?: return@post call.respond(
                HttpStatusCode.BadRequest,
                mapOf("error" to "Server name is required")
            )
            val toolName = call.parameters["toolName"] ?: return@post call.respond(
                HttpStatusCode.BadRequest,
                mapOf("error" to "Tool name is required")
            )

            val arguments = try {
                call.receive<Map<String, Any>>()
            } catch (e: Exception) {
                emptyMap()
            }

            val result = clientManager.callTool(serverName, toolName, arguments)
            if (result.isSuccess) {
                call.respond(HttpStatusCode.OK, result.getOrNull() ?: emptyMap<String, Any>())
            } else {
                call.respond(
                    HttpStatusCode.InternalServerError,
                    mapOf("error" to result.exceptionOrNull()?.message)
                )
            }
        }

        // 列出服务器资源
        get("/api/mcp/servers/{name}/resources") {
            val serverName = call.parameters["name"] ?: return@get call.respond(
                HttpStatusCode.BadRequest,
                mapOf("error" to "Server name is required")
            )

            val result = clientManager.listResources(serverName)
            if (result.isSuccess) {
                call.respond(HttpStatusCode.OK, result.getOrNull() ?: emptyList<Map<String, Any>>())
            } else {
                call.respond(
                    HttpStatusCode.InternalServerError,
                    mapOf("error" to result.exceptionOrNull()?.message)
                )
            }
        }

        // 读取资源
        get("/api/mcp/servers/{name}/resources/{uri}") {
            val serverName = call.parameters["name"] ?: return@get call.respond(
                HttpStatusCode.BadRequest,
                mapOf("error" to "Server name is required")
            )
            val uri = call.parameters["uri"] ?: return@get call.respond(
                HttpStatusCode.BadRequest,
                mapOf("error" to "Resource URI is required")
            )

            val result = clientManager.readResource(serverName, uri)
            if (result.isSuccess) {
                call.respond(HttpStatusCode.OK, result.getOrNull() ?: emptyMap<String, Any>())
            } else {
                call.respond(
                    HttpStatusCode.InternalServerError,
                    mapOf("error" to result.exceptionOrNull()?.message)
                )
            }
        }

        // 重新加载配置
        post("/api/mcp/config/reload") {
            val config = clientManager.reloadConfig()
            if (config != null) {
                call.respond(HttpStatusCode.OK, mapOf("message" to "Configuration reloaded successfully"))
            } else {
                call.respond(
                    HttpStatusCode.InternalServerError,
                    mapOf("error" to "Failed to reload configuration")
                )
            }
        }
    }
}