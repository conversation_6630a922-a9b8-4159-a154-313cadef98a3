package com.phodal.archlift.mcp.config

import kotlinx.serialization.Serializable

/**
 * MCP 配置文件的根结构
 */
@Serializable
data class McpConfig(
    val mcpServers: Map<String, McpServerConfig>
)

/**
 * MCP 传输方式类型
 */
@Serializable
enum class McpTransportType {
    STDIO,
    SSE,
    HTTP
}

/**
 * 单个 MCP Server 的配置
 */
@Serializable
data class McpServerConfig(
    val command: String? = null,
    val args: List<String> = emptyList(),
    val env: Map<String, String> = emptyMap(),
    val disabled: Boolean = false,
    val autoApprove: List<String> = emptyList(),
    val workingDirectory: String? = null,
    val timeout: Long = 30000L, // 30 seconds default timeout
    val transport: McpTransportType = McpTransportType.STDIO,
    val url: String? = null, // For SSE and HTTP transports
    val headers: Map<String, String> = emptyMap() // For HTTP transports
)

/**
 * MCP Server 运行时状态
 */
enum class McpServerStatus {
    STOPPED,
    STARTING,
    RUNNING,
    ERROR,
    DISABLED
}

/**
 * MCP Server 运行时信息
 */
data class McpServerInstance(
    val name: String,
    val config: McpServerConfig,
    var status: McpServerStatus = McpServerStatus.STOPPED,
    var process: Process? = null,
    var lastError: String? = null,
    var startTime: Long? = null,
    var client: Any? = null,
    var transport: com.phodal.archlift.mcp.transport.McpTransport? = null
)
