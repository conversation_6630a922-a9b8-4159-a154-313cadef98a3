package com.phodal.archlift.mcp.client

import com.phodal.archlift.mcp.config.*
import com.phodal.archlift.mcp.transport.McpTransportFactory
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.json.Json
import java.nio.file.Path
import java.nio.file.Paths
import kotlin.io.path.exists
import kotlin.io.path.readText

/**
 * MCP 客户端管理器
 * 负责管理多个 MCP 服务器连接和生命周期
 */
class McpClientManager(
    private val configPath: Path = Paths.get("mcp-config.json"),
    private val scope: CoroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
) {
    private val json = Json {
        ignoreUnknownKeys = true
        prettyPrint = true
    }

    private var _config: McpConfig? = null
    private val serverInstances = mutableMapOf<String, McpServerInstance>()
    private val _serverStates = MutableStateFlow<Map<String, McpServerStatus>>(emptyMap())

    /**
     * 获取当前配置
     */
    val config: McpConfig?
        get() = _config

    /**
     * 获取所有服务器实例
     */
    val servers: Map<String, McpServerInstance>
        get() = serverInstances.toMap()

    /**
     * 获取服务器状态流
     */
    val serverStates: Flow<Map<String, McpServerStatus>>
        get() = _serverStates.asStateFlow()

    /**
     * 加载配置文件
     */
    suspend fun loadConfig(): McpConfig? {
        return try {
            if (!configPath.exists()) {
                createDefaultConfig()
                return null
            }

            val configText = configPath.readText()
            val config = json.decodeFromString<McpConfig>(configText)
            _config = config

            // 更新服务器实例
            updateServerInstances(config)
            updateServerStates()
            
            config
        } catch (e: Exception) {
            println("Failed to load MCP config: ${e.message}")
            null
        }
    }

    /**
     * 重新加载配置
     */
    suspend fun reloadConfig(): McpConfig? {
        // 停止所有运行中的服务器
        stopAllServers()
        return loadConfig()
    }

    /**
     * 获取指定名称的服务器实例
     */
    fun getServer(name: String): McpServerInstance? {
        return serverInstances[name]
    }

    /**
     * 获取所有启用的服务器
     */
    fun getEnabledServers(): List<McpServerInstance> {
        return serverInstances.values.filter { !it.config.disabled }
    }

    /**
     * 启动指定的服务器
     */
    suspend fun startServer(name: String): Result<Unit> {
        val instance = serverInstances[name] 
            ?: return Result.failure(IllegalArgumentException("Server not found: $name"))

        if (instance.config.disabled) {
            return Result.failure(IllegalStateException("Server is disabled: $name"))
        }

        if (instance.status == McpServerStatus.RUNNING) {
            return Result.success(Unit)
        }

        return try {
            updateServerStatus(name, McpServerStatus.STARTING)

            // 验证配置
            McpTransportFactory.validateConfig(instance.config).getOrThrow()

            // 创建传输
            val transport = McpTransportFactory.createTransport(instance.config)
            instance.transport = transport

            // 连接传输
            transport.connect().getOrThrow()

            // 创建客户端 (暂时使用占位符)
            val client = "McpClient:${name}"
            instance.client = client

            updateServerStatus(name, McpServerStatus.RUNNING)
            instance.startTime = System.currentTimeMillis()

            // 监控连接状态
            scope.launch {
                transport.getConnectionState().collect { state ->
                    when (state) {
                        com.phodal.archlift.mcp.transport.ConnectionState.ERROR -> {
                            updateServerStatus(name, McpServerStatus.ERROR, "Connection error")
                        }
                        com.phodal.archlift.mcp.transport.ConnectionState.DISCONNECTED -> {
                            if (instance.status == McpServerStatus.RUNNING) {
                                updateServerStatus(name, McpServerStatus.STOPPED)
                            }
                        }
                        else -> { /* 其他状态暂不处理 */ }
                    }
                }
            }

            Result.success(Unit)
        } catch (e: Exception) {
            updateServerStatus(name, McpServerStatus.ERROR, e.message)
            Result.failure(e)
        }
    }

    /**
     * 停止指定的服务器
     */
    suspend fun stopServer(name: String): Result<Unit> {
        val instance = serverInstances[name] 
            ?: return Result.failure(IllegalArgumentException("Server not found: $name"))

        return try {
            instance.transport?.disconnect()
            instance.client = null
            instance.transport = null
            instance.process = null
            instance.startTime = null
            
            updateServerStatus(name, McpServerStatus.STOPPED)
            Result.success(Unit)
        } catch (e: Exception) {
            updateServerStatus(name, McpServerStatus.ERROR, e.message)
            Result.failure(e)
        }
    }

    /**
     * 停止所有服务器
     */
    suspend fun stopAllServers() {
        val runningServers = serverInstances.keys.filter { 
            serverInstances[it]?.status == McpServerStatus.RUNNING 
        }
        
        runningServers.forEach { name ->
            stopServer(name)
        }
    }

    /**
     * 重启指定的服务器
     */
    suspend fun restartServer(name: String): Result<Unit> {
        stopServer(name)
        delay(1000) // 等待 1 秒
        return startServer(name)
    }

    /**
     * 调用服务器工具
     */
    suspend fun callTool(serverName: String, toolName: String, arguments: Map<String, Any>): Result<Map<String, Any>> {
        val instance = getServer(serverName)
            ?: return Result.failure(IllegalArgumentException("Server not found: $serverName"))

        val client = instance.client
            ?: return Result.failure(IllegalStateException("Server not connected: $serverName"))

        return try {
            // 暂时返回模拟结果
            val result = mapOf(
                "tool" to toolName,
                "arguments" to arguments.toString(),
                "result" to "Tool execution simulated"
            )
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 列出服务器工具
     */
    suspend fun listTools(serverName: String): Result<List<Map<String, Any>>> {
        val instance = getServer(serverName)
            ?: return Result.failure(IllegalArgumentException("Server not found: $serverName"))

        val client = instance.client
            ?: return Result.failure(IllegalStateException("Server not connected: $serverName"))

        return try {
            // 暂时返回模拟工具列表
            val result = listOf(
                mapOf("name" to "example_tool", "description" to "Example tool")
            )
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 列出服务器资源
     */
    suspend fun listResources(serverName: String): Result<List<Map<String, Any>>> {
        val instance = getServer(serverName)
            ?: return Result.failure(IllegalArgumentException("Server not found: $serverName"))

        val client = instance.client
            ?: return Result.failure(IllegalStateException("Server not connected: $serverName"))

        return try {
            // 暂时返回模拟资源列表
            val result = listOf(
                mapOf("uri" to "file://example.txt", "name" to "Example Resource")
            )
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 读取服务器资源
     */
    suspend fun readResource(serverName: String, uri: String): Result<Map<String, Any>> {
        val instance = getServer(serverName)
            ?: return Result.failure(IllegalArgumentException("Server not found: $serverName"))

        val client = instance.client
            ?: return Result.failure(IllegalStateException("Server not connected: $serverName"))

        return try {
            // 暂时返回模拟资源内容
            val result = mapOf(
                "uri" to uri,
                "content" to "Simulated resource content",
                "mimeType" to "text/plain"
            )
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 更新服务器状态
     */
    private fun updateServerStatus(name: String, status: McpServerStatus, error: String? = null) {
        serverInstances[name]?.let { instance ->
            instance.status = status
            instance.lastError = error
            updateServerStates()
        }
    }

    /**
     * 更新服务器状态流
     */
    private fun updateServerStates() {
        val states = serverInstances.mapValues { it.value.status }
        _serverStates.value = states
    }

    /**
     * 创建默认配置文件
     */
    private fun createDefaultConfig() {
        val defaultConfig = McpConfig(
            mcpServers = mapOf(
                "example-stdio-server" to McpServerConfig(
                    command = "node",
                    args = listOf("server.js"),
                    disabled = true,
                    transport = McpTransportType.STDIO,
                    autoApprove = emptyList()
                ),
                "example-sse-server" to McpServerConfig(
                    url = "http://localhost:3000/sse",
                    disabled = true,
                    transport = McpTransportType.SSE,
                    autoApprove = emptyList()
                ),
                "example-http-server" to McpServerConfig(
                    url = "http://localhost:3000/mcp",
                    disabled = true,
                    transport = McpTransportType.HTTP,
                    autoApprove = emptyList()
                )
            )
        )

        try {
            val configText = json.encodeToString(McpConfig.serializer(), defaultConfig)
            configPath.toFile().writeText(configText)
            println("Created default MCP config at: $configPath")
        } catch (e: Exception) {
            println("Failed to create default config: ${e.message}")
        }
    }

    /**
     * 更新服务器实例
     */
    private fun updateServerInstances(config: McpConfig) {
        // 停止不再存在的服务器
        val configServerNames = config.mcpServers.keys
        val removedServers = serverInstances.keys - configServerNames
        
        scope.launch {
            removedServers.forEach { name ->
                stopServer(name)
                serverInstances.remove(name)
            }
        }

        // 添加或更新服务器实例
        config.mcpServers.forEach { (name, serverConfig) ->
            val existingInstance = serverInstances[name]
            if (existingInstance != null) {
                // 如果配置发生变化，需要重启服务器
                if (existingInstance.config != serverConfig && existingInstance.status == McpServerStatus.RUNNING) {
                    scope.launch {
                        restartServer(name)
                    }
                }
                // 更新现有实例的配置
                serverInstances[name] = existingInstance.copy(config = serverConfig)
            } else {
                // 创建新实例
                serverInstances[name] = McpServerInstance(
                    name = name,
                    config = serverConfig,
                    status = if (serverConfig.disabled) McpServerStatus.DISABLED else McpServerStatus.STOPPED
                )
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        scope.launch {
            stopAllServers()
        }
        scope.cancel()
    }
}
