package com.phodal.archlift.mcp.transport

import kotlinx.coroutines.flow.Flow

/**
 * MCP 传输层接口
 * 统一不同传输方式的接口
 */
interface McpTransport {
    /**
     * 连接到 MCP 服务器
     */
    suspend fun connect(): Result<Unit>
    
    /**
     * 断开连接
     */
    suspend fun disconnect()
    
    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean
    
    /**
     * 获取底层的客户端传输
     */
    fun getClientTransport(): Any
    
    /**
     * 获取连接状态流
     */
    fun getConnectionState(): Flow<ConnectionState>
}

/**
 * 连接状态
 */
enum class ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    ERROR,
    RECONNECTING
}

/**
 * 传输配置基类
 */
abstract class TransportConfig {
    abstract val timeout: Long
}

/**
 * STDIO 传输配置
 */
data class StdioTransportConfig(
    val command: String,
    val args: List<String> = emptyList(),
    val env: Map<String, String> = emptyMap(),
    val workingDirectory: String? = null,
    override val timeout: Long = 30000L
) : TransportConfig()

/**
 * SSE 传输配置
 */
data class SseTransportConfig(
    val url: String,
    val headers: Map<String, String> = emptyMap(),
    override val timeout: Long = 30000L
) : TransportConfig()

/**
 * HTTP 传输配置
 */
data class HttpTransportConfig(
    val url: String,
    val headers: Map<String, String> = emptyMap(),
    override val timeout: Long = 30000L
) : TransportConfig()
