package com.phodal.archlift.mcp.transport

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withTimeout
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * STDIO MCP 传输实现
 */
class StdioMcpTransport(
    private val config: StdioTransportConfig
) : McpTransport {
    
    private var process: Process? = null
    private var clientTransport: Any? = null
    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    
    override suspend fun connect(): Result<Unit> {
        return try {
            _connectionState.value = ConnectionState.CONNECTING
            
            // 启动进程
            val processBuilder = ProcessBuilder(listOf(config.command) + config.args)
            
            // 设置环境变量
            if (config.env.isNotEmpty()) {
                processBuilder.environment().putAll(config.env)
            }
            
            // 设置工作目录
            config.workingDirectory?.let { workDir ->
                processBuilder.directory(File(workDir))
            }
            
            // 启动进程
            process = withTimeout(config.timeout) {
                processBuilder.start()
            }
            
            val currentProcess = process ?: throw IllegalStateException("Failed to start process")
            
            // 创建 STDIO 传输 (暂时使用占位符)
            clientTransport = "StdioTransport:${currentProcess.hashCode()}"
            
            _connectionState.value = ConnectionState.CONNECTED
            Result.success(Unit)
            
        } catch (e: Exception) {
            _connectionState.value = ConnectionState.ERROR
            cleanup()
            Result.failure(e)
        }
    }
    
    override suspend fun disconnect() {
        _connectionState.value = ConnectionState.DISCONNECTED
        cleanup()
    }
    
    override fun isConnected(): Boolean {
        return _connectionState.value == ConnectionState.CONNECTED && 
               process?.isAlive == true
    }
    
    override fun getClientTransport(): Any {
        return clientTransport ?: throw IllegalStateException("Transport not connected")
    }
    
    override fun getConnectionState(): Flow<ConnectionState> {
        return _connectionState.asStateFlow()
    }
    
    private fun cleanup() {
        try {
            clientTransport = null
            process?.let { proc ->
                if (proc.isAlive) {
                    // 尝试优雅关闭
                    proc.outputStream.close()
                    proc.inputStream.close()
                    proc.errorStream.close()
                    
                    // 等待进程结束，最多等待 5 秒
                    if (!proc.waitFor(5, TimeUnit.SECONDS)) {
                        // 强制终止
                        proc.destroyForcibly()
                    }
                }
            }
        } catch (e: Exception) {
            // 忽略清理过程中的异常
        } finally {
            process = null
        }
    }
}
