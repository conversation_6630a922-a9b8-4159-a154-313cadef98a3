package com.phodal.archlift.mcp.transport

import com.phodal.archlift.mcp.config.McpServerConfig
import com.phodal.archlift.mcp.config.McpTransportType

/**
 * MCP 传输工厂
 * 根据配置创建相应的传输实现
 */
object McpTransportFactory {
    
    /**
     * 根据服务器配置创建传输实例
     */
    fun createTransport(config: McpServerConfig): McpTransport {
        return when (config.transport) {
            McpTransportType.STDIO -> {
                val command = config.command 
                    ?: throw IllegalArgumentException("Command is required for STDIO transport")
                
                StdioMcpTransport(
                    StdioTransportConfig(
                        command = command,
                        args = config.args,
                        env = config.env,
                        workingDirectory = config.workingDirectory,
                        timeout = config.timeout
                    )
                )
            }
            
            McpTransportType.SSE -> {
                val url = config.url 
                    ?: throw IllegalArgumentException("URL is required for SSE transport")
                
                SseMcpTransport(
                    SseTransportConfig(
                        url = url,
                        headers = config.headers,
                        timeout = config.timeout
                    )
                )
            }
            
            McpTransportType.HTTP -> {
                val url = config.url 
                    ?: throw IllegalArgumentException("URL is required for HTTP transport")
                
                HttpMcpTransport(
                    HttpTransportConfig(
                        url = url,
                        headers = config.headers,
                        timeout = config.timeout
                    )
                )
            }
        }
    }
    
    /**
     * 验证配置是否有效
     */
    fun validateConfig(config: McpServerConfig): Result<Unit> {
        return try {
            when (config.transport) {
                McpTransportType.STDIO -> {
                    if (config.command.isNullOrBlank()) {
                        throw IllegalArgumentException("Command is required for STDIO transport")
                    }
                }
                
                McpTransportType.SSE, McpTransportType.HTTP -> {
                    if (config.url.isNullOrBlank()) {
                        throw IllegalArgumentException("URL is required for ${config.transport} transport")
                    }
                    
                    // 简单的 URL 格式验证
                    if (!config.url.startsWith("http://") && !config.url.startsWith("https://")) {
                        throw IllegalArgumentException("Invalid URL format: ${config.url}")
                    }
                }
            }
            
            if (config.timeout <= 0) {
                throw IllegalArgumentException("Timeout must be positive")
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
