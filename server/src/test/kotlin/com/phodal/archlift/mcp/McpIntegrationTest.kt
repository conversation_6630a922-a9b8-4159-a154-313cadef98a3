package com.phodal.archlift.mcp

import com.phodal.archlift.mcp.config.McpTransportType
import com.phodal.archlift.mcp.transport.McpTransportFactory
import com.phodal.archlift.mcp.config.McpServerConfig
import kotlin.test.*

class McpIntegrationTest {

    @Test
    fun testTransportFactory() {
        // 测试 STDIO 传输配置验证
        val stdioConfig = McpServerConfig(
            command = "echo",
            args = listOf("hello"),
            transport = McpTransportType.STDIO
        )
        
        val stdioValidation = McpTransportFactory.validateConfig(stdioConfig)
        assertTrue(stdioValidation.isSuccess, "STDIO config should be valid")
        
        // 测试 SSE 传输配置验证
        val sseConfig = McpServerConfig(
            url = "http://localhost:3000/sse",
            transport = McpTransportType.SSE
        )
        
        val sseValidation = McpTransportFactory.validateConfig(sseConfig)
        assertTrue(sseValidation.isSuccess, "SSE config should be valid")
        
        // 测试 HTTP 传输配置验证
        val httpConfig = McpServerConfig(
            url = "https://api.example.com/mcp",
            transport = McpTransportType.HTTP,
            headers = mapOf("Authorization" to "Bearer token")
        )
        
        val httpValidation = McpTransportFactory.validateConfig(httpConfig)
        assertTrue(httpValidation.isSuccess, "HTTP config should be valid")
    }

    @Test
    fun testInvalidConfigurations() {
        // 测试无效的 STDIO 配置（缺少命令）
        val invalidStdioConfig = McpServerConfig(
            transport = McpTransportType.STDIO
        )
        
        val stdioValidation = McpTransportFactory.validateConfig(invalidStdioConfig)
        assertTrue(stdioValidation.isFailure, "STDIO config without command should be invalid")
        
        // 测试无效的 SSE 配置（缺少 URL）
        val invalidSseConfig = McpServerConfig(
            transport = McpTransportType.SSE
        )
        
        val sseValidation = McpTransportFactory.validateConfig(invalidSseConfig)
        assertTrue(sseValidation.isFailure, "SSE config without URL should be invalid")
        
        // 测试无效的 HTTP 配置（无效 URL）
        val invalidHttpConfig = McpServerConfig(
            url = "invalid-url",
            transport = McpTransportType.HTTP
        )
        
        val httpValidation = McpTransportFactory.validateConfig(invalidHttpConfig)
        assertTrue(httpValidation.isFailure, "HTTP config with invalid URL should be invalid")
    }

    @Test
    fun testTransportCreation() {
        // 测试创建 STDIO 传输
        val stdioConfig = McpServerConfig(
            command = "echo",
            args = listOf("hello"),
            transport = McpTransportType.STDIO
        )
        
        val stdioTransport = McpTransportFactory.createTransport(stdioConfig)
        assertNotNull(stdioTransport, "Should create STDIO transport")
        
        // 测试创建 SSE 传输
        val sseConfig = McpServerConfig(
            url = "http://localhost:3000/sse",
            transport = McpTransportType.SSE
        )
        
        val sseTransport = McpTransportFactory.createTransport(sseConfig)
        assertNotNull(sseTransport, "Should create SSE transport")
        
        // 测试创建 HTTP 传输
        val httpConfig = McpServerConfig(
            url = "https://api.example.com/mcp",
            transport = McpTransportType.HTTP
        )
        
        val httpTransport = McpTransportFactory.createTransport(httpConfig)
        assertNotNull(httpTransport, "Should create HTTP transport")
    }

    @Test
    fun testConfigurationSerialization() {
        // 测试配置序列化和反序列化
        val config = McpServerConfig(
            command = "java",
            args = listOf("-jar", "server.jar"),
            env = mapOf("ENV" to "test"),
            disabled = false,
            autoApprove = listOf("tool1", "tool2"),
            workingDirectory = "/tmp",
            timeout = 60000L,
            transport = McpTransportType.STDIO,
            url = null,
            headers = emptyMap()
        )
        
        // 验证配置属性
        assertEquals("java", config.command)
        assertEquals(listOf("-jar", "server.jar"), config.args)
        assertEquals(mapOf("ENV" to "test"), config.env)
        assertFalse(config.disabled)
        assertEquals(listOf("tool1", "tool2"), config.autoApprove)
        assertEquals("/tmp", config.workingDirectory)
        assertEquals(60000L, config.timeout)
        assertEquals(McpTransportType.STDIO, config.transport)
        assertNull(config.url)
        assertTrue(config.headers.isEmpty())
    }
}
