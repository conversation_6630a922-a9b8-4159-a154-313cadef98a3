package com.phodal.archlift.mcp

import com.phodal.archlift.mcp.client.McpClientManager
import com.phodal.archlift.mcp.config.McpServerConfig
import com.phodal.archlift.mcp.config.McpTransportType
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.Json
import java.nio.file.Files
import java.nio.file.Paths
import kotlin.test.*

class McpClientManagerTest {

    private val json = Json {
        ignoreUnknownKeys = true
        prettyPrint = true
    }

    @Test
    fun testLoadConfig() = runBlocking {
        // 创建临时配置文件
        val tempConfigPath = Files.createTempFile("mcp-config", ".json")
        val testConfig = """
        {
            "mcpServers": {
                "test-stdio": {
                    "command": "echo",
                    "args": ["hello"],
                    "transport": "STDIO",
                    "disabled": false
                },
                "test-sse": {
                    "url": "http://localhost:3000/sse",
                    "transport": "SSE",
                    "disabled": true
                },
                "test-http": {
                    "url": "http://localhost:3000/mcp",
                    "transport": "HTTP",
                    "disabled": true,
                    "headers": {
                        "Authorization": "Bearer test"
                    }
                }
            }
        }
        """.trimIndent()
        
        Files.write(tempConfigPath, testConfig.toByteArray())
        
        try {
            val clientManager = McpClientManager(tempConfigPath)
            val config = clientManager.loadConfig()
            
            assertNotNull(config)
            assertEquals(3, config.mcpServers.size)
            
            // 测试 STDIO 配置
            val stdioConfig = config.mcpServers["test-stdio"]
            assertNotNull(stdioConfig)
            assertEquals("echo", stdioConfig.command)
            assertEquals(McpTransportType.STDIO, stdioConfig.transport)
            assertFalse(stdioConfig.disabled)
            
            // 测试 SSE 配置
            val sseConfig = config.mcpServers["test-sse"]
            assertNotNull(sseConfig)
            assertEquals("http://localhost:3000/sse", sseConfig.url)
            assertEquals(McpTransportType.SSE, sseConfig.transport)
            assertTrue(sseConfig.disabled)
            
            // 测试 HTTP 配置
            val httpConfig = config.mcpServers["test-http"]
            assertNotNull(httpConfig)
            assertEquals("http://localhost:3000/mcp", httpConfig.url)
            assertEquals(McpTransportType.HTTP, httpConfig.transport)
            assertTrue(httpConfig.disabled)
            assertEquals("Bearer test", httpConfig.headers["Authorization"])
            
        } finally {
            Files.deleteIfExists(tempConfigPath)
        }
    }

    @Test
    fun testServerManagement() = runBlocking {
        val tempConfigPath = Files.createTempFile("mcp-config", ".json")
        val testConfig = """
        {
            "mcpServers": {
                "test-server": {
                    "command": "echo",
                    "args": ["test"],
                    "transport": "STDIO",
                    "disabled": false
                }
            }
        }
        """.trimIndent()
        
        Files.write(tempConfigPath, testConfig.toByteArray())
        
        try {
            val clientManager = McpClientManager(tempConfigPath)
            clientManager.loadConfig()
            
            // 测试获取服务器
            val server = clientManager.getServer("test-server")
            assertNotNull(server)
            assertEquals("test-server", server.name)
            
            // 测试启动服务器
            val startResult = clientManager.startServer("test-server")
            assertTrue(startResult.isSuccess)
            
            // 测试停止服务器
            val stopResult = clientManager.stopServer("test-server")
            assertTrue(stopResult.isSuccess)
            
            // 测试不存在的服务器
            val invalidResult = clientManager.startServer("non-existent")
            assertTrue(invalidResult.isFailure)
            
        } finally {
            Files.deleteIfExists(tempConfigPath)
        }
    }

    @Test
    fun testToolOperations() = runBlocking {
        val tempConfigPath = Files.createTempFile("mcp-config", ".json")
        val testConfig = """
        {
            "mcpServers": {
                "test-server": {
                    "command": "echo",
                    "args": ["test"],
                    "transport": "STDIO",
                    "disabled": false
                }
            }
        }
        """.trimIndent()
        
        Files.write(tempConfigPath, testConfig.toByteArray())
        
        try {
            val clientManager = McpClientManager(tempConfigPath)
            clientManager.loadConfig()
            clientManager.startServer("test-server")
            
            // 测试列出工具
            val toolsResult = clientManager.listTools("test-server")
            assertTrue(toolsResult.isSuccess)
            val tools = toolsResult.getOrNull()
            assertNotNull(tools)
            assertTrue(tools.isNotEmpty())
            
            // 测试调用工具
            val callResult = clientManager.callTool("test-server", "example_tool", mapOf("param" to "value"))
            assertTrue(callResult.isSuccess)
            val result = callResult.getOrNull()
            assertNotNull(result)
            
        } finally {
            Files.deleteIfExists(tempConfigPath)
        }
    }

    @Test
    fun testResourceOperations() = runBlocking {
        val tempConfigPath = Files.createTempFile("mcp-config", ".json")
        val testConfig = """
        {
            "mcpServers": {
                "test-server": {
                    "command": "echo",
                    "args": ["test"],
                    "transport": "STDIO",
                    "disabled": false
                }
            }
        }
        """.trimIndent()
        
        Files.write(tempConfigPath, testConfig.toByteArray())
        
        try {
            val clientManager = McpClientManager(tempConfigPath)
            clientManager.loadConfig()
            clientManager.startServer("test-server")
            
            // 测试列出资源
            val resourcesResult = clientManager.listResources("test-server")
            assertTrue(resourcesResult.isSuccess)
            val resources = resourcesResult.getOrNull()
            assertNotNull(resources)
            
            // 测试读取资源
            val readResult = clientManager.readResource("test-server", "file://example.txt")
            assertTrue(readResult.isSuccess)
            val content = readResult.getOrNull()
            assertNotNull(content)
            
        } finally {
            Files.deleteIfExists(tempConfigPath)
        }
    }
}
