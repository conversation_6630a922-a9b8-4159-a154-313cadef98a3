{"mcpServers": {"java-parser": {"command": "java", "args": ["-jar", "mcps/java-parser/build/libs/java-parser-1.0.0.jar"], "disabled": false, "autoApprove": ["parse_java_code"], "workingDirectory": ".", "timeout": 30000, "transport": "STDIO"}, "example-stdio-server": {"command": "node", "args": ["server.js"], "disabled": true, "transport": "STDIO"}, "example-sse-server": {"url": "http://localhost:3000/sse", "disabled": true, "transport": "SSE", "timeout": 30000}, "example-http-server": {"url": "http://localhost:3000/mcp", "disabled": true, "transport": "HTTP", "timeout": 30000, "headers": {"Authorization": "Bearer token123"}}}}