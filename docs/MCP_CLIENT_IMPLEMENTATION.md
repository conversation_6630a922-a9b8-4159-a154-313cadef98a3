# MCP 客户端实现文档

## 概述

本文档描述了基于 MCP Kotlin SDK 重新实现的 MCP 客户端管理器，支持三种传输方式：StdioClientTransport、SSEClientTransport 和 StreamableHttpClientTransport。

## 架构设计

### 核心组件

1. **McpClientManager** - 主要的客户端管理器
2. **传输层接口** - 统一的传输抽象
3. **传输实现** - 三种具体的传输实现
4. **配置模型** - 扩展的配置支持

### 类图

```
McpClientManager
├── McpTransportFactory
├── McpTransport (接口)
│   ├── StdioMcpTransport
│   ├── SseMcpTransport
│   └── HttpMcpTransport
└── McpConfig
    └── McpServerConfig
```

## 支持的传输方式

### 1. STDIO 传输 (StdioClientTransport)

用于通过标准输入输出与本地进程通信。

**配置示例：**
```json
{
  "transport": "STDIO",
  "command": "java",
  "args": ["-jar", "server.jar"],
  "workingDirectory": ".",
  "env": {
    "ENV_VAR": "value"
  }
}
```

### 2. SSE 传输 (SSEClientTransport)

用于通过 Server-Sent Events 与远程服务器通信。

**配置示例：**
```json
{
  "transport": "SSE",
  "url": "http://localhost:3000/sse",
  "headers": {
    "Authorization": "Bearer token"
  }
}
```

### 3. HTTP 传输 (StreamableHttpClientTransport)

用于通过 HTTP 流与远程服务器通信。

**配置示例：**
```json
{
  "transport": "HTTP",
  "url": "https://api.example.com/mcp",
  "headers": {
    "Authorization": "Bearer token",
    "Content-Type": "application/json"
  }
}
```

## 主要功能

### 服务器管理

- **启动服务器**: `POST /api/mcp/servers/{name}/start`
- **停止服务器**: `POST /api/mcp/servers/{name}/stop`
- **重启服务器**: `POST /api/mcp/servers/{name}/restart`
- **列出服务器**: `GET /api/mcp/servers`

### 工具调用

- **列出工具**: `GET /api/mcp/servers/{name}/tools`
- **调用工具**: `POST /api/mcp/servers/{name}/tools/{toolName}`

### 资源访问

- **列出资源**: `GET /api/mcp/servers/{name}/resources`
- **读取资源**: `GET /api/mcp/servers/{name}/resources/{uri}`

### 配置管理

- **重新加载配置**: `POST /api/mcp/config/reload`
- **健康检查**: `GET /api/mcp/health`

## 配置文件格式

完整的配置文件示例：

```json
{
  "mcpServers": {
    "java-parser": {
      "command": "java",
      "args": ["-jar", "mcps/java-parser/build/libs/java-parser-1.0.0.jar"],
      "disabled": false,
      "autoApprove": ["parse_java_code"],
      "workingDirectory": ".",
      "timeout": 30000,
      "transport": "STDIO"
    },
    "remote-sse-server": {
      "url": "http://localhost:3000/sse",
      "disabled": true,
      "transport": "SSE",
      "timeout": 30000,
      "headers": {
        "Authorization": "Bearer token123"
      }
    },
    "remote-http-server": {
      "url": "https://api.example.com/mcp",
      "disabled": true,
      "transport": "HTTP",
      "timeout": 30000,
      "headers": {
        "Authorization": "Bearer token123",
        "Content-Type": "application/json"
      }
    }
  }
}
```

## 使用示例

### 启动服务器

```bash
curl -X POST http://localhost:8080/api/mcp/servers/java-parser/start
```

### 查看服务器状态

```bash
curl http://localhost:8080/api/mcp/servers
```

### 调用工具

```bash
curl -X POST -H "Content-Type: application/json" \
  -d '{"code": "public class Test {}"}' \
  http://localhost:8080/api/mcp/servers/java-parser/tools/parse_java_code
```

## 测试

项目包含以下测试：

1. **McpIntegrationTest** - 集成测试，验证传输工厂和配置
2. **McpClientManagerTest** - 单元测试，验证客户端管理器功能

运行测试：
```bash
./gradlew :server:test --tests "*Mcp*Test*"
```

## 扩展性

### 添加新的传输方式

1. 实现 `McpTransport` 接口
2. 在 `McpTransportFactory` 中添加新的传输类型
3. 更新 `McpTransportType` 枚举
4. 更新配置模型以支持新的参数

### 添加新的功能

客户端管理器提供了扩展点，可以轻松添加：

- 新的 API 端点
- 自定义认证机制
- 连接池管理
- 监控和日志记录

## 注意事项

1. **当前实现状态**: 这是一个基础实现，传输层使用了占位符，需要根据实际的 MCP Kotlin SDK API 进行调整
2. **错误处理**: 实现了基本的错误处理，但可能需要根据具体需求进行增强
3. **安全性**: 在生产环境中使用时，需要添加适当的认证和授权机制
4. **性能**: 当前实现适用于中小规模部署，大规模使用时可能需要优化

## 下一步

1. 根据实际的 MCP Kotlin SDK API 完善传输层实现
2. 添加更完善的错误处理和重试机制
3. 实现连接池和资源管理
4. 添加监控和指标收集
5. 完善文档和示例
