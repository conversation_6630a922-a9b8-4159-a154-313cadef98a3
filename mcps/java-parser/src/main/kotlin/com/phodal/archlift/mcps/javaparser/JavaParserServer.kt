package com.phodal.archlift.mcps.javaparser

import com.github.javaparser.JavaParser
import kotlinx.serialization.json.Json
import java.io.File

/**
 * Java Parser 简化版本
 * 提供 Java 代码解析功能
 */
fun main() {
    println("Java Parser Server started successfully")
    println("This is a simplified version for demonstration")

    // 示例：解析一个简单的 Java 代码
    val sampleCode = """
        package com.example;

        public class HelloWorld {
            private String message;

            public HelloWorld(String message) {
                this.message = message;
            }

            public void sayHello() {
                System.out.println(message);
            }
        }
    """.trimIndent()

    try {
        val result = parseJavaCode(sampleCode)
        println("Parse result: $result")
    } catch (e: Exception) {
        println("Error: ${e.message}")
    }
}

/**
 * 解析 Java 代码
 */
fun parseJavaCode(code: String): Map<String, Any> {
    val parser = JavaParser()
    val parseResult = parser.parse(code)

    if (!parseResult.isSuccessful) {
        return mapOf(
            "success" to false,
            "error" to "Failed to parse Java code: ${parseResult.problems}"
        )
    }

    val cu = parseResult.result.get()
    val packageName = cu.packageDeclaration.map { it.nameAsString }.orElse("default")
    val imports = cu.imports.map { it.nameAsString }
    val classes = cu.types.map { it.nameAsString }

    return mapOf(
        "success" to true,
        "packageName" to packageName,
        "imports" to imports,
        "classes" to classes
    )
}




